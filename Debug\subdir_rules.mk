################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Each subdirectory must supply rules for building sources it contributes
build-1497059023: ../empty.syscfg
	@echo 'Building file: "$<"'
	@echo 'Invoking: SysConfig'
	"D:/desk/CCS/ccs/utils/sysconfig_1.24.0/sysconfig_cli.bat" --script "C:/Users/<USER>/workspace_ccstheia/TI_CAR1.1/empty.syscfg" -o "." -s "D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/.metadata/product.json" --compiler ticlang
	@echo 'Finished building: "$<"'
	@echo ' '

device_linker.cmd: build-1497059023 ../empty.syscfg
device.opt: build-1497059023
device.cmd.genlibs: build-1497059023
ti_msp_dl_config.c: build-1497059023
ti_msp_dl_config.h: build-1497059023
Event.dot: build-1497059023

%.o: ./%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"D:/desk/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1.1" -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1.1/Debug" -I"D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include" -I"D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source" -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1.1/BSP/Inc" -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1.1/APP/Inc" -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1.1/DMP" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -gdwarf-3 -w -MMD -MP -MF"$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '

startup_mspm0g350x_ticlang.o: D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/ti/devices/msp/m0p/startup_system_files/ticlang/startup_mspm0g350x_ticlang.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"D:/desk/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1.1" -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1.1/Debug" -I"D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include" -I"D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source" -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1.1/BSP/Inc" -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1.1/APP/Inc" -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1.1/DMP" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -gdwarf-3 -w -MMD -MP -MF"$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '

%.o: ../%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"D:/desk/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1.1" -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1.1/Debug" -I"D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include" -I"D:/desk/mspmssdk/mspm0_sdk_2_05_00_05/source" -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1.1/BSP/Inc" -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1.1/APP/Inc" -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1.1/DMP" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -gdwarf-3 -w -MMD -MP -MF"$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '


